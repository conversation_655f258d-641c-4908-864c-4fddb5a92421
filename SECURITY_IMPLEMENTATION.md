# WatermarkExtension 权限控制实施方案

## 概述

本文档描述了为 WatermarkExtension 实施的权限控制措施，确保只有 XWikiAdminGroup 组的用户可以查看和编辑水印配置。

## 实施的安全措施

### 1. 页面级权限控制

#### WatermarkAdmin.xml
- **添加了 XWiki.XWikiRights 对象**
  - `groups`: XWiki.XWikiAdminGroup
  - `levels`: view,edit
  - `allow`: 1 (允许)
- **作用**: 在系统级别阻止非管理员用户访问配置页面

### 2. 代码级权限检查

#### WatermarkAdmin.xml
- **权限验证逻辑**:
  ```velocity
  ## 检查当前用户是否为管理员组成员
  #set($currentUser = $xcontext.user)
  #set($isAdmin = false)
  
  #if($currentUser && $currentUser != 'XWiki.XWikiGuest')
    #set($userDoc = $xwiki.getDocument($currentUser))
    #if($userDoc)
      #set($isAdmin = $userDoc.getObject('XWiki.XWikiGroups', 'member', 'XWiki.XWikiAdminGroup'))
      #if(!$isAdmin)
        #set($isAdmin = $services.user.group.getAllGroupsReferencesForUser($userDoc.documentReference).contains($services.model.resolveDocument('XWiki.XWikiAdminGroup')))
      #end
    #end
  #end
  ```
- **非管理员用户**: 显示"访问被拒绝"消息，阻止访问配置界面

#### WebHome.xml
- **条件显示逻辑**: 
  - 管理员用户: 显示完整的配置链接和管理界面
  - 普通用户: 只显示当前状态，隐藏配置链接
  - 显示友好的权限提示信息

### 3. 配置提供者安全增强

#### WatermarkConfigProvider.xml
- **智能配置暴露**:
  - 只有在管理员启用水印时才提供实际配置
  - 未启用时提供最小化的默认配置
  - 防止敏感配置信息泄露

## 安全特性

### 双重保护机制
1. **页面级权限**: XWiki 系统级的访问控制
2. **代码级检查**: 应用层的权限验证和用户体验优化

### 用户体验优化
- **管理员用户**: 完整的配置访问权限
- **普通用户**: 友好的权限提示，清晰的访问限制说明
- **未登录用户**: 基本的状态查看，无配置访问权限

### 配置安全
- **敏感信息保护**: 配置详情只对管理员可见
- **功能完整性**: 水印功能正常工作，不影响最终用户体验
- **渐进式权限**: 根据用户权限级别提供不同的访问体验

## 权限检查逻辑

### 用户分类
1. **XWikiAdminGroup 成员**: 完全访问权限
2. **已登录普通用户**: 只读状态查看
3. **未登录用户 (XWiki.XWikiGuest)**: 基本状态查看

### 检查方法
1. **主要方法**: `$userDoc.getObject('XWiki.XWikiGroups', 'member', 'XWiki.XWikiAdminGroup')`
2. **备用方法**: `$services.user.group.getAllGroupsReferencesForUser()` 服务
3. **双重验证**: 确保权限检查的可靠性

## 文件修改清单

### 修改的文件
1. **WatermarkAdmin.xml**
   - 添加 XWiki.XWikiRights 权限对象
   - 添加权限检查代码
   - 添加访问拒绝提示

2. **WebHome.xml**
   - 添加条件显示逻辑
   - 隐藏非管理员的配置链接
   - 添加权限说明

3. **WatermarkConfigProvider.xml**
   - 添加配置启用检查
   - 实施智能配置暴露
   - 保护敏感配置信息

## 测试建议

### 测试场景
1. **管理员用户测试**:
   - 验证完整的配置访问权限
   - 确认所有管理功能正常

2. **普通用户测试**:
   - 验证配置链接被隐藏
   - 确认状态查看功能正常
   - 验证友好的权限提示

3. **未登录用户测试**:
   - 验证基本功能可用
   - 确认无配置访问权限
   - 验证水印功能正常工作

### 验证要点
- [ ] 非管理员无法访问 WatermarkAdmin 页面
- [ ] WebHome 页面根据用户权限显示不同内容
- [ ] 水印功能对所有用户正常工作
- [ ] 配置信息只对管理员可见
- [ ] 权限提示信息友好清晰

## 部署说明

1. **构建项目**: `mvn clean package`
2. **部署 XAR**: 上传生成的 `.xar` 文件到 XWiki
3. **权限验证**: 使用不同权限级别的用户测试功能
4. **配置检查**: 确认水印配置只对管理员可见

## 技术实施细节

### Velocity 脚本优化
- **统一脚本块**: 所有权限检查逻辑都在单一的 `{{velocity}}{{/velocity}}` 块中
- **变量作用域**: 确保 `$isAdmin` 变量在整个页面中可用
- **XML 兼容性**: 正确转义 `&&` 为 `&amp;&amp;` 以符合 XML 规范

### 权限检查算法
```velocity
## 双重验证机制
#set($isAdmin = $userDoc.getObject('XWiki.XWikiGroups', 'member', 'XWiki.XWikiAdminGroup'))
#if(!$isAdmin)
  #set($isAdmin = $services.user.group.getAllGroupsReferencesForUser($userDoc.documentReference).contains($services.model.resolveDocument('XWiki.XWikiAdminGroup')))
#end
```

## 注意事项

- 权限控制不影响水印的正常显示功能
- 配置更改仍然保存在 XWikiPreferences 中
- 系统管理员可以通过 XWiki 管理界面调整权限设置
- 建议定期审查用户组成员资格
- Velocity 脚本必须保持在连续的块中，避免分割导致变量作用域问题
