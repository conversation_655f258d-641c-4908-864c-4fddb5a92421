<?xml version="1.1" encoding="UTF-8"?>

<!--
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
-->

<xwikidoc version="1.5" reference="WatermarkExtension.WatermarkConfigProvider" locale="">
  <web>WatermarkExtension</web>
  <name>WatermarkConfigProvider</name>
  <language/>
  <defaultLanguage/>
  <translation>0</translation>
  <creator>xwiki:XWiki.Admin</creator>
  <parent>WatermarkExtension.WebHome</parent>
  <author>xwiki:XWiki.Admin</author>
  <contentAuthor>xwiki:XWiki.Admin</contentAuthor>
  <version>1.1</version>
  <title>Watermark Configuration Provider</title>
  <comment/>
  <minorEdit>false</minorEdit>
  <syntaxId>xwiki/2.1</syntaxId>
  <hidden>true</hidden>
  <content><![CDATA[{{velocity}}
## Public watermark configuration provider
## This page provides watermark configuration for anonymous users
## who cannot access the REST API

#set($preferencesDoc = $xwiki.getDocument('XWiki.XWikiPreferences'))
#set($watermarkConfig = $preferencesDoc.getObject('WatermarkExtension.WatermarkConfigClass'))

## Default configuration
#set($enabled = 0)
#set($textTemplate = '${user} - ${timestamp}')
#set($xSpacing = 200)
#set($ySpacing = 100)
#set($angle = -30)
#set($opacity = 0.1)
#set($fontSize = 14)
#set($antiCopy = 0)
#set($applyToMobile = 1)

## Get current values if config exists
#if($watermarkConfig)
  #set($enabledProp = $watermarkConfig.getProperty('enabled'))
  #if($enabledProp && $enabledProp.value)
    #set($enabled = $enabledProp.value)
  #end
  
  #set($textTemplateProp = $watermarkConfig.getProperty('textTemplate'))
  #if($textTemplateProp && $textTemplateProp.value && $textTemplateProp.value != '')
    #set($textTemplate = $textTemplateProp.value)
  #end
  
  #set($xSpacingProp = $watermarkConfig.getProperty('xSpacing'))
  #if($xSpacingProp && $xSpacingProp.value && $xSpacingProp.value != '')
    #set($xSpacing = $xSpacingProp.value)
  #end
  
  #set($ySpacingProp = $watermarkConfig.getProperty('ySpacing'))
  #if($ySpacingProp && $ySpacingProp.value && $ySpacingProp.value != '')
    #set($ySpacing = $ySpacingProp.value)
  #end
  
  #set($angleProp = $watermarkConfig.getProperty('angle'))
  #if($angleProp && $angleProp.value && $angleProp.value != '')
    #set($angle = $angleProp.value)
  #end
  
  #set($opacityProp = $watermarkConfig.getProperty('opacity'))
  #if($opacityProp && $opacityProp.value && $opacityProp.value != '')
    #set($opacity = $opacityProp.value)
  #end
  
  #set($fontSizeProp = $watermarkConfig.getProperty('fontSize'))
  #if($fontSizeProp && $fontSizeProp.value && $fontSizeProp.value != '')
    #set($fontSize = $fontSizeProp.value)
  #end
  
  #set($antiCopyProp = $watermarkConfig.getProperty('antiCopy'))
  #if($antiCopyProp && $antiCopyProp.value)
    #set($antiCopy = $antiCopyProp.value)
  #end
  
  #set($applyToMobileProp = $watermarkConfig.getProperty('applyToMobile'))
  #if($applyToMobileProp && $applyToMobileProp.value)
    #set($applyToMobile = $applyToMobileProp.value)
  #end
#end

## Escape text template for JavaScript
#set($safeTextTemplate = $textTemplate.replaceAll("'", "\'"))

## Output JavaScript configuration
window.xwikiWatermarkPublicConfig = {
  enabled: #if($enabled == 1)true#{else}false#end,
  textTemplate: '$safeTextTemplate',
  xSpacing: $xSpacing,
  ySpacing: $ySpacing,
  angle: $angle,
  opacity: $opacity,
  fontSize: $fontSize,
  antiCopy: #if($antiCopy == 1)true#{else}false#end,
  applyToMobile: #if($applyToMobile == 1)true#{else}false#end
};
{{/velocity}}]]></content>
</xwikidoc>
