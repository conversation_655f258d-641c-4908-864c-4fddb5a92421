<?xml version="1.1" encoding="UTF-8"?>

<!--
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
-->

<xwikidoc version="1.5" reference="WatermarkExtension.WatermarkConfigClass" locale="">
  <web>WatermarkExtension</web>
  <name>WatermarkConfigClass</name>
  <language/>
  <defaultLanguage/>
  <translation>0</translation>
  <creator>xwiki:XWiki.Admin</creator>
  <parent>WatermarkExtension.WebHome</parent>
  <author>xwiki:XWiki.Admin</author>
  <contentAuthor>xwiki:XWiki.Admin</contentAuthor>
  <version>1.1</version>
  <title>Watermark Configuration Class</title>
  <comment/>
  <minorEdit>false</minorEdit>
  <syntaxId>xwiki/2.1</syntaxId>
  <hidden>true</hidden>
  <content>{{include reference="XWiki.ClassSheet"/}}</content>
  <class>
    <name>WatermarkExtension.WatermarkConfigClass</name>
    <customClass/>
    <customMapping/>
    <defaultViewSheet/>
    <defaultEditSheet/>
    <defaultWeb/>
    <nameField/>
    <validationScript/>
    <enabled>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.BooleanClass</classType>
      <customDisplay/>
      <defaultValue>0</defaultValue>
      <displayFormType>checkbox</displayFormType>
      <displayType>yesno</displayType>
      <name>enabled</name>
      <number>1</number>
      <prettyName>Enable Watermark</prettyName>
      <unmodifiable>0</unmodifiable>
      <validationMessage/>
      <validationRegExp/>
    </enabled>
    <textTemplate>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.TextAreaClass</classType>
      <customDisplay/>
      <defaultValue>${user} - ${timestamp}</defaultValue>
      <editor>Text</editor>
      <name>textTemplate</name>
      <number>2</number>
      <prettyName>Watermark Text Template</prettyName>
      <rows>3</rows>
      <size>60</size>
      <unmodifiable>0</unmodifiable>
      <validationMessage/>
      <validationRegExp/>
    </textTemplate>
    <xSpacing>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.NumberClass</classType>
      <customDisplay/>
      <defaultValue>200</defaultValue>
      <name>xSpacing</name>
      <number>3</number>
      <numberType>integer</numberType>
      <prettyName>Horizontal Spacing (px)</prettyName>
      <size>10</size>
      <unmodifiable>0</unmodifiable>
      <validationMessage>Value must be between 50 and 500</validationMessage>
      <validationRegExp>^([5-9][0-9]|[1-4][0-9][0-9]|500)$</validationRegExp>
    </xSpacing>
    <ySpacing>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.NumberClass</classType>
      <customDisplay/>
      <defaultValue>100</defaultValue>
      <name>ySpacing</name>
      <number>4</number>
      <numberType>integer</numberType>
      <prettyName>Vertical Spacing (px)</prettyName>
      <size>10</size>
      <unmodifiable>0</unmodifiable>
      <validationMessage>Value must be between 50 and 500</validationMessage>
      <validationRegExp>^([5-9][0-9]|[1-4][0-9][0-9]|500)$</validationRegExp>
    </ySpacing>
    <angle>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.NumberClass</classType>
      <customDisplay/>
      <defaultValue>-30</defaultValue>
      <name>angle</name>
      <number>5</number>
      <numberType>integer</numberType>
      <prettyName>Rotation Angle (degrees)</prettyName>
      <size>10</size>
      <unmodifiable>0</unmodifiable>
      <validationMessage>Value must be between -180 and 180</validationMessage>
      <validationRegExp>^-?(1[0-7][0-9]|180|[1-9]?[0-9])$</validationRegExp>
    </angle>
    <opacity>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.NumberClass</classType>
      <customDisplay/>
      <defaultValue>0.1</defaultValue>
      <name>opacity</name>
      <number>6</number>
      <numberType>float</numberType>
      <prettyName>Opacity (0.0-1.0)</prettyName>
      <size>10</size>
      <unmodifiable>0</unmodifiable>
      <validationMessage>Value must be between 0.0 and 1.0</validationMessage>
      <validationRegExp>^(0(\.[0-9]+)?|1(\.0+)?)$</validationRegExp>
    </opacity>
    <fontSize>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.NumberClass</classType>
      <customDisplay/>
      <defaultValue>14</defaultValue>
      <name>fontSize</name>
      <number>7</number>
      <numberType>integer</numberType>
      <prettyName>Font Size (px)</prettyName>
      <size>10</size>
      <unmodifiable>0</unmodifiable>
      <validationMessage>Value must be between 8 and 48</validationMessage>
      <validationRegExp>^([8-9]|[1-3][0-9]|4[0-8])$</validationRegExp>
    </fontSize>
    <antiCopy>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.BooleanClass</classType>
      <customDisplay/>
      <defaultValue>0</defaultValue>
      <displayFormType>checkbox</displayFormType>
      <displayType>yesno</displayType>
      <name>antiCopy</name>
      <number>8</number>
      <prettyName>Enable Anti-Copy Protection</prettyName>
      <unmodifiable>0</unmodifiable>
      <validationMessage/>
      <validationRegExp/>
    </antiCopy>
    <applyToMobile>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.BooleanClass</classType>
      <customDisplay/>
      <defaultValue>1</defaultValue>
      <displayFormType>checkbox</displayFormType>
      <displayType>yesno</displayType>
      <name>applyToMobile</name>
      <number>9</number>
      <prettyName>Apply to Mobile Devices</prettyName>
      <unmodifiable>0</unmodifiable>
      <validationMessage/>
      <validationRegExp/>
    </applyToMobile>
  </class>
</xwikidoc>
