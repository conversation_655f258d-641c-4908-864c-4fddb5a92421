<?xml version="1.1" encoding="UTF-8"?>

<!--
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
-->

<xwikidoc version="1.5" reference="WatermarkExtension.WebHome" locale="">
  <web>WatermarkExtension</web>
  <name>WebHome</name>
  <language/>
  <defaultLanguage/>
  <translation>0</translation>
  <creator>xwiki:XWiki.Admin</creator>
  <parent>Main.WebHome</parent>
  <author>xwiki:XWiki.Admin</author>
  <contentAuthor>xwiki:XWiki.Admin</contentAuthor>
  <version>1.1</version>
  <title>Watermark Extension</title>
  <comment/>
  <minorEdit>false</minorEdit>
  <syntaxId>xwiki/2.1</syntaxId>
  <hidden>false</hidden>
  <content><![CDATA[

= Watermark Extension =

This extension provides dynamic watermark functionality for XWiki pages.

== Features ==

* **Dynamic Canvas Watermarks**: High-quality text watermarks rendered using HTML5 Canvas API
* **Placeholder Support**: Dynamic replacement of `${user}` and `${timestamp}` placeholders
* **Comprehensive Configuration**: 8 configurable parameters for complete customization
* **Anti-Copy Protection**: Prevent content copying with configurable protection features
* **Mobile Optimization**: Responsive design with automatic mobile device adaptation
* **Multi-Language Support**: Built-in Chinese and English translations

== Configuration ==

{{velocity}}
## Check if current user has admin privileges
#set($currentUser = $xcontext.user)
#set($isAdmin = false)

## Check if user is in XWikiAdminGroup
#if($currentUser && $currentUser != 'XWiki.XWikiGuest')
  ## Use XWiki's built-in method to check if user is in admin group
  #set($isAdmin = $xwiki.getUser($currentUser).isUserInGroup('XWiki.XWikiAdminGroup'))

  ## Fallback: check using access level
  #if(!$isAdmin)
    #set($isAdmin = $xwiki.hasAccessLevel('admin', $currentUser, 'XWiki.XWikiPreferences'))
  #end
#end

#if($isAdmin)

{{info}}
**Administrator Access Required**: You need administrator privileges to configure watermark settings.
{{/info}}

[[**Configure Watermark Settings**>>WatermarkExtension.WatermarkAdmin]]

#else

{{warning}}
**Configuration Access Restricted**

Watermark configuration is restricted to administrators only. Only members of the **XWikiAdminGroup** can access the configuration interface.

If you need to configure watermark settings, please contact your system administrator.
{{/warning}}

#end

== Current Status ==

#set($watermarkConfig = $xwiki.getDocument('XWiki.XWikiPreferences').getObject('WatermarkExtension.WatermarkConfigClass'))
#if($watermarkConfig)
  #set($enabled = $watermarkConfig.getProperty('enabled').value)
  #if($enabled == 1)
    {{success}}**Watermark is currently ENABLED**{{/success}}
    
    Current settings:
    * Text Template: $watermarkConfig.getProperty('textTemplate').value
    * Horizontal Spacing: $watermarkConfig.getProperty('xSpacing').value px
    * Vertical Spacing: $watermarkConfig.getProperty('ySpacing').value px
    * Rotation Angle: $watermarkConfig.getProperty('angle').value°
    * Opacity: $watermarkConfig.getProperty('opacity').value
    * Font Size: $watermarkConfig.getProperty('fontSize').value px
    * Anti-Copy Protection: #if($watermarkConfig.getProperty('antiCopy').value == 1)Enabled#{else}Disabled#end
    * Apply to Mobile: #if($watermarkConfig.getProperty('applyToMobile').value == 1)Yes#{else}No#end
  #else
    {{warning}}**Watermark is currently DISABLED**{{/warning}}
  #end
#else
  {{error}}**Watermark configuration not found**{{/error}}
  
  The watermark configuration object has not been created yet. Please visit the [[configuration page>>WatermarkExtension.WatermarkAdmin]] to initialize the settings.
#end
== Quick Actions ==

#if($isAdmin)
* [[Configure Watermark>>WatermarkExtension.WatermarkAdmin]] - Access the full configuration interface
#end
* [[View Engine Code>>WatermarkExtension.WatermarkEngine]] - View the watermark rendering engine
* [[View Styles>>WatermarkExtension.WatermarkStyles]] - View anti-copy protection and mobile styles

== Documentation ==

=== Configuration Parameters ===

| Parameter | Type | Range | Default | Description |
|-----------|------|-------|---------|-------------|
| **Enabled** | Boolean | - | `false` | Enable/disable watermark display |
| **Text Template** | String | - | `${user} - ${timestamp}` | Watermark text with placeholder support |
| **Horizontal Spacing** | Number | 50-500 | `200` | Horizontal spacing between watermarks (px) |
| **Vertical Spacing** | Number | 50-500 | `100` | Vertical spacing between watermarks (px) |
| **Rotation Angle** | Number | -180 to 180 | `-30` | Text rotation angle (degrees) |
| **Opacity** | Number | 0.0-1.0 | `0.1` | Watermark transparency level |
| **Font Size** | Number | 8-48 | `14` | Text font size (px) |
| **Anti-Copy Protection** | Boolean | - | `false` | Enable anti-copy protection features |
| **Apply to Mobile** | Boolean | - | `true` | Show watermarks on mobile devices |

=== Placeholder Variables ===

* **`${user}`**: Replaced with current user's name
* **`${timestamp}`**: Replaced with current date and time

== Troubleshooting ==

If watermarks are not appearing:

1. **Check Configuration**: Ensure watermark is enabled in the configuration
2. **Browser Console**: Check for JavaScript errors in browser developer tools
3. **Network Requests**: Verify that watermark scripts are loading correctly
4. **Canvas Support**: Ensure your browser supports HTML5 Canvas API

== Version Information ==

* **Extension Version**: 1.0-SNAPSHOT
* **XWiki Compatibility**: 15.10.13+
* **Last Updated**: $xwiki.formatDate($doc.date)
{{/velocity}}
]]>
</content>
</xwikidoc>
