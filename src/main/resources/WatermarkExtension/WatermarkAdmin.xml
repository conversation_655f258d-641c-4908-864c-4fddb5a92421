<?xml version="1.1" encoding="UTF-8"?>

<!--
 * See the NOTICE file distributed with this work for additional
 * information regarding copyright ownership.
 *
 * This is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as
 * published by the Free Software Foundation; either version 2.1 of
 * the License, or (at your option) any later version.
 *
 * This software is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this software; if not, write to the Free
 * Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
 * 02110-1301 USA, or see the FSF site: http://www.fsf.org.
-->

<xwikidoc version="1.5" reference="WatermarkExtension.WatermarkAdmin" locale="">
  <web>WatermarkExtension</web>
  <name>WatermarkAdmin</name>
  <language/>
  <defaultLanguage/>
  <translation>0</translation>
  <creator>xwiki:XWiki.Admin</creator>
  <parent>WatermarkExtension.WebHome</parent>
  <author>xwiki:XWiki.Admin</author>
  <contentAuthor>xwiki:XWiki.Admin</contentAuthor>
  <version>1.1</version>
  <title>Watermark Configuration</title>
  <comment/>
  <minorEdit>false</minorEdit>
  <syntaxId>xwiki/2.1</syntaxId>
  <hidden>false</hidden>
  <object>
    <name>WatermarkExtension.WatermarkAdmin</name>
    <number>0</number>
    <className>XWiki.AdminSheetClass</className>
    <guid>watermark-admin-sheet</guid>
    <class>
      <name>XWiki.AdminSheetClass</name>
      <customClass/>
      <customMapping/>
      <defaultViewSheet/>
      <defaultEditSheet/>
      <defaultWeb/>
      <nameField/>
      <validationScript/>
      <menu>
        <cache>0</cache>
        <disabled>0</disabled>
        <displayType>input</displayType>
        <name>menu</name>
        <number>1</number>
        <prettyName>Menu</prettyName>
        <size>30</size>
        <unmodifiable>0</unmodifiable>
        <classType>com.xpn.xwiki.objects.classes.StringClass</classType>
      </menu>
      <section>
        <cache>0</cache>
        <disabled>0</disabled>
        <displayType>input</displayType>
        <name>section</name>
        <number>2</number>
        <prettyName>Section</prettyName>
        <size>30</size>
        <unmodifiable>0</unmodifiable>
        <classType>com.xpn.xwiki.objects.classes.StringClass</classType>
      </section>
    </class>
    <property>
      <menu>Look &amp; Feel</menu>
    </property>
    <property>
      <section>Watermark</section>
    </property>
  </object>
  <object>
    <name>WatermarkExtension.WatermarkAdmin</name>
    <number>0</number>
    <className>XWiki.XWikiRights</className>
    <guid>watermark-admin-rights-allow</guid>
    <class>
      <name>XWiki.XWikiRights</name>
      <customClass/>
      <customMapping/>
      <defaultViewSheet/>
      <defaultEditSheet/>
      <defaultWeb/>
      <nameField/>
      <validationScript/>
      <allow>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.BooleanClass</classType>
        <customDisplay/>
        <defaultValue>1</defaultValue>
        <displayFormType>select</displayFormType>
        <displayType>allow</displayType>
        <name>allow</name>
        <number>4</number>
        <prettyName>Allow/Deny</prettyName>
        <unmodifiable>0</unmodifiable>
        <validationMessage/>
        <validationRegExp/>
      </allow>
      <groups>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.GroupsClass</classType>
        <customDisplay/>
        <defaultValue/>
        <name>groups</name>
        <number>1</number>
        <prettyName>Groups</prettyName>
        <size>5</size>
        <unmodifiable>0</unmodifiable>
        <validationMessage/>
        <validationRegExp/>
      </groups>
      <levels>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.LevelsClass</classType>
        <customDisplay/>
        <defaultValue/>
        <name>levels</name>
        <number>2</number>
        <prettyName>Levels</prettyName>
        <size>3</size>
        <unmodifiable>0</unmodifiable>
        <validationMessage/>
        <validationRegExp/>
      </levels>
      <users>
        <cache>0</cache>
        <classType>com.xpn.xwiki.objects.classes.UsersClass</classType>
        <customDisplay/>
        <defaultValue/>
        <name>users</name>
        <number>3</number>
        <prettyName>Users</prettyName>
        <size>5</size>
        <unmodifiable>0</unmodifiable>
        <validationMessage/>
        <validationRegExp/>
      </users>
    </class>
    <property>
      <allow>1</allow>
    </property>
    <property>
      <groups>XWiki.XWikiAdminGroup</groups>
    </property>
    <property>
      <levels>view,edit</levels>
    </property>
    <property>
      <users/>
    </property>
  </object>
  <content><![CDATA[
{{velocity}}
## Check if current user has admin privileges
#set($currentUser = $xcontext.user)
#set($isAdmin = false)

## Check if user is in XWikiAdminGroup
#if($currentUser && $currentUser != 'XWiki.XWikiGuest')
  ## Use XWiki's built-in method to check if user is in admin group
  #set($isAdmin = $xwiki.getUser($currentUser).isUserInGroup('XWiki.XWikiAdminGroup'))

  ## Fallback: check using access level
  #if(!$isAdmin)
    #set($isAdmin = $xwiki.hasAccessLevel('admin', $currentUser, 'XWiki.XWikiPreferences'))
  #end
#end

## Check if user has admin privileges
#if($isAdmin)
## User has admin privileges, proceed with configuration

#set($watermarkConfigClass = $xwiki.getDocument('WatermarkExtension.WatermarkConfigClass'))
#set($preferencesDoc = $xwiki.getDocument('XWiki.XWikiPreferences'))
#set($watermarkConfig = $preferencesDoc.getObject('WatermarkExtension.WatermarkConfigClass'))

## Create config object if it doesn't exist
#if(!$watermarkConfig)
  #set($watermarkConfig = $preferencesDoc.newObject('WatermarkExtension.WatermarkConfigClass'))
  ## Set default values for new object
  #set($discard = $watermarkConfig.set('enabled', 0))
  #set($discard = $watermarkConfig.set('textTemplate', '${user} - ${timestamp}'))
  #set($discard = $watermarkConfig.set('xSpacing', 200))
  #set($discard = $watermarkConfig.set('ySpacing', 100))
  #set($discard = $watermarkConfig.set('angle', -30))
  #set($discard = $watermarkConfig.set('opacity', 0.1))
  #set($discard = $watermarkConfig.set('fontSize', 14))
  #set($discard = $watermarkConfig.set('antiCopy', 0))
  #set($discard = $watermarkConfig.set('applyToMobile', 1))
  #set($discard = $preferencesDoc.save())
#end

## Handle form submission
#if($request.action == 'save')
  #set($watermarkConfig = $preferencesDoc.getObject('WatermarkExtension.WatermarkConfigClass'))
  #if($watermarkConfig)
    ## Handle checkbox values (1 if checked, 0 if not)
    #set($enabledValue = 0)
    #if($request.enabled == '1')
      #set($enabledValue = 1)
    #end
    #set($antiCopyValue = 0)
    #if($request.antiCopy == '1')
      #set($antiCopyValue = 1)
    #end
    #set($applyToMobileValue = 0)
    #if($request.applyToMobile == '1')
      #set($applyToMobileValue = 1)
    #end

    #set($discard = $watermarkConfig.set('enabled', $enabledValue))
    #set($discard = $watermarkConfig.set('textTemplate', $request.textTemplate))
    #set($discard = $watermarkConfig.set('xSpacing', $numbertool.toNumber($request.xSpacing)))
    #set($discard = $watermarkConfig.set('ySpacing', $numbertool.toNumber($request.ySpacing)))
    #set($discard = $watermarkConfig.set('angle', $numbertool.toNumber($request.angle)))
    #set($discard = $watermarkConfig.set('opacity', $numbertool.toNumber($request.opacity)))
    #set($discard = $watermarkConfig.set('fontSize', $numbertool.toNumber($request.fontSize)))
    #set($discard = $watermarkConfig.set('antiCopy', $antiCopyValue))
    #set($discard = $watermarkConfig.set('applyToMobile', $applyToMobileValue))
    #set($discard = $preferencesDoc.save())
    #set($saveMessage = 'Watermark configuration saved successfully')
  #end
#end

## Handle reset to defaults
#if($request.action == 'reset')
  #set($watermarkConfig = $preferencesDoc.getObject('WatermarkExtension.WatermarkConfigClass'))
  #if($watermarkConfig)
    #set($discard = $watermarkConfig.set('enabled', 0))
    #set($discard = $watermarkConfig.set('textTemplate', '${user} - ${timestamp}'))
    #set($discard = $watermarkConfig.set('xSpacing', 200))
    #set($discard = $watermarkConfig.set('ySpacing', 100))
    #set($discard = $watermarkConfig.set('angle', -30))
    #set($discard = $watermarkConfig.set('opacity', 0.1))
    #set($discard = $watermarkConfig.set('fontSize', 14))
    #set($discard = $watermarkConfig.set('antiCopy', 0))
    #set($discard = $watermarkConfig.set('applyToMobile', 1))
    #set($discard = $preferencesDoc.save())
    #set($resetMessage = 'Watermark configuration reset to default values')
  #end
#end

## Get current values with safe property access
#if($watermarkConfig)
  #set($enabledProp = $watermarkConfig.getProperty('enabled'))
  #set($enabled = $enabledProp.value)
  #if(!$enabled)
    #set($enabled = 0)
  #end

  #set($textTemplateProp = $watermarkConfig.getProperty('textTemplate'))
  #set($textTemplate = $textTemplateProp.value)
  #if(!$textTemplate || $textTemplate == '')
    #set($textTemplate = '${user} - ${timestamp}')
  #end

  #set($xSpacingProp = $watermarkConfig.getProperty('xSpacing'))
  #set($xSpacing = $xSpacingProp.value)
  #if(!$xSpacing || $xSpacing == '')
    #set($xSpacing = 200)
  #end

  #set($ySpacingProp = $watermarkConfig.getProperty('ySpacing'))
  #set($ySpacing = $ySpacingProp.value)
  #if(!$ySpacing || $ySpacing == '')
    #set($ySpacing = 100)
  #end

  #set($angleProp = $watermarkConfig.getProperty('angle'))
  #set($angle = $angleProp.value)
  #if(!$angle || $angle == '')
    #set($angle = -30)
  #end

  #set($opacityProp = $watermarkConfig.getProperty('opacity'))
  #set($opacity = $opacityProp.value)
  #if(!$opacity || $opacity == '')
    #set($opacity = 0.1)
  #end

  #set($fontSizeProp = $watermarkConfig.getProperty('fontSize'))
  #set($fontSize = $fontSizeProp.value)
  #if(!$fontSize || $fontSize == '')
    #set($fontSize = 14)
  #end

  #set($antiCopyProp = $watermarkConfig.getProperty('antiCopy'))
  #set($antiCopy = $antiCopyProp.value)
  #if(!$antiCopy)
    #set($antiCopy = 0)
  #end

  #set($applyToMobileProp = $watermarkConfig.getProperty('applyToMobile'))
  #set($applyToMobile = $applyToMobileProp.value)
  #if(!$applyToMobile)
    #set($applyToMobile = 1)
  #end
#else
  ## Set default values if no config object exists
  #set($enabled = 0)
  #set($textTemplate = '${user} - ${timestamp}')
  #set($xSpacing = 200)
  #set($ySpacing = 100)
  #set($angle = -30)
  #set($opacity = 0.1)
  #set($fontSize = 14)
  #set($antiCopy = 0)
  #set($applyToMobile = 1)
#end

## Set default values if empty
#if(!$textTemplate || $textTemplate == '')
  #set($textTemplate = '${user} - ${timestamp}')
#end
#if(!$xSpacing || $xSpacing == '')
  #set($xSpacing = 200)
#end
#if(!$ySpacing || $ySpacing == '')
  #set($ySpacing = 100)
#end
#if(!$angle || $angle == '')
  #set($angle = -30)
#end
#if(!$opacity || $opacity == '')
  #set($opacity = 0.1)
#end
#if(!$fontSize || $fontSize == '')
  #set($fontSize = 14)
#end
= Watermark Configuration =

#if($saveMessage)
  {{info}}$saveMessage{{/info}}
#end
#if($resetMessage)
  {{info}}$resetMessage{{/info}}
#end

{{html}}
<div class="watermark-admin-container">
  <div class="row">
    <div class="col-md-8">
      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title">Watermark Configuration</h3>
        </div>
        <div class="panel-body">
          <form action="" method="post" class="watermark-config-form">
            <input type="hidden" name="action" value="save" />

            <!-- Enable/Disable Watermark -->
            <div class="form-group">
              <label class="checkbox">
                <input type="checkbox" name="enabled" value="1"#if($enabled == 1) checked="checked"#end />
                Enable Watermark
              </label>
              <p class="help-block">Enable or disable the watermark display across the wiki</p>
            </div>

            <!-- Text Template -->
            <div class="form-group">
              <label for="textTemplate">Watermark Text Template</label>
              <textarea class="form-control" name="textTemplate" id="textTemplate" rows="2" placeholder="${user} - ${timestamp}">$!textTemplate</textarea>
              <p class="help-block">Text template for the watermark. Use ${user} for current user and ${timestamp} for current time</p>
            </div>
            
            <!-- Spacing Configuration -->
            <div class="row">
              <div class="col-md-6">
                <div class="form-group">
                  <label for="xSpacing">Horizontal Spacing</label>
                  <input type="number" class="form-control" name="xSpacing" id="xSpacing" value="$!xSpacing" min="50" max="500" />
                  <p class="help-block">Horizontal spacing between watermarks in pixels (50-500)</p>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-group">
                  <label for="ySpacing">Vertical Spacing</label>
                  <input type="number" class="form-control" name="ySpacing" id="ySpacing" value="$!ySpacing" min="50" max="500" />
                  <p class="help-block">Vertical spacing between watermarks in pixels (50-500)</p>
                </div>
              </div>
            </div>
            
            <!-- Style Configuration -->
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="angle">Rotation Angle</label>
                  <input type="number" class="form-control" name="angle" id="angle" value="$!angle" min="-180" max="180" />
                  <p class="help-block">Rotation angle of the watermark text in degrees (-180 to 180)</p>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="opacity">Opacity</label>
                  <input type="number" class="form-control" name="opacity" id="opacity" value="$!opacity" min="0" max="1" step="0.1" />
                  <p class="help-block">Transparency level of the watermark (0.0 = transparent, 1.0 = opaque)</p>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-group">
                  <label for="fontSize">Font Size</label>
                  <input type="number" class="form-control" name="fontSize" id="fontSize" value="$!fontSize" min="8" max="48" />
                  <p class="help-block">Font size of the watermark text in pixels (8-48)</p>
                </div>
              </div>
            </div>
            
            <!-- Advanced Options -->
            <div class="form-group">
              <label class="checkbox">
                <input type="checkbox" name="antiCopy" value="1"#if($antiCopy == 1) checked="checked"#end />
                Anti-Copy Protection
              </label>
              <p class="help-block">Enable anti-copy protection to prevent content copying and right-click</p>
            </div>

            <div class="form-group">
              <label class="checkbox">
                <input type="checkbox" name="applyToMobile" value="1"#if($applyToMobile == 1) checked="checked"#end />
                Apply to Mobile Devices
              </label>
              <p class="help-block">Enable watermark display on mobile devices and tablets</p>
            </div>

            <!-- Action Buttons -->
            <div class="form-group">
              <button type="submit" class="btn btn-primary">
                <span class="glyphicon glyphicon-floppy-disk"></span>
                Save Configuration
              </button>
              <button type="button" class="btn btn-default" onclick="resetToDefaults()">
                <span class="glyphicon glyphicon-refresh"></span>
                Reset to Defaults
              </button>
              <button type="button" class="btn btn-info" onclick="previewWatermark()">
                <span class="glyphicon glyphicon-eye-open"></span>
                Preview Watermark
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
    
    <!-- Preview Panel -->
    <div class="col-md-4">
      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title">Preview Watermark</h3>
        </div>
        <div class="panel-body">
          <div id="watermark-preview" style="position: relative; height: 200px; border: 1px solid #ddd; background: #f9f9f9; overflow: hidden;">
            <div style="padding: 10px; font-size: 12px; color: #666;">
              Watermark preview will appear here
            </div>
          </div>
          <p class="help-block">This preview shows how the watermark will appear on pages</p>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
// Watermark Admin JavaScript
(function() {
  'use strict';

  // Reset form to default values
  window.resetToDefaults = function() {
    if (confirm('Are you sure you want to reset all watermark settings to default values?')) {
      var form = document.querySelector('.watermark-config-form');
      var resetForm = document.createElement('form');
      resetForm.method = 'post';
      resetForm.innerHTML = '<input type="hidden" name="action" value="reset" />';
      document.body.appendChild(resetForm);
      resetForm.submit();
    }
  };

  // Preview watermark with current settings
  window.previewWatermark = function() {
    var config = getCurrentConfig();
    var previewDiv = document.getElementById('watermark-preview');

    // Clear existing preview content
    previewDiv.innerHTML = '';

    if (!config.enabled) {
      previewDiv.innerHTML = '<div style="padding: 10px; text-align: center; color: #999;">Watermark is disabled</div>';
      return;
    }

    // Create preview canvas
    var canvas = document.createElement('canvas');
    var ctx = canvas.getContext('2d');

    canvas.width = previewDiv.offsetWidth;
    canvas.height = previewDiv.offsetHeight;
    canvas.style.position = 'absolute';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.opacity = config.opacity;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Get processed text with placeholders replaced
    var text = processPlaceholders(config.textTemplate);

    // Set text properties (matching WatermarkEngine.xml)
    ctx.font = config.fontSize + 'px Arial, sans-serif';
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';  // Keep consistent with engine
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    // Draw watermark grid (matching WatermarkEngine.xml logic)
    for (var x = 0; x < canvas.width + config.xSpacing; x += config.xSpacing) {
      for (var y = 0; y < canvas.height + config.ySpacing; y += config.ySpacing) {
        ctx.save();

        // Move to position and rotate
        ctx.translate(x, y);
        ctx.rotate((config.angle * Math.PI) / 180);

        // Draw text
        ctx.fillText(text, 0, 0);

        ctx.restore();
      }
    }

    previewDiv.appendChild(canvas);
  };

  // Get current form configuration
  function getCurrentConfig() {
    var form = document.querySelector('.watermark-config-form');
    return {
      enabled: form.querySelector('[name="enabled"]').checked,
      textTemplate: form.querySelector('[name="textTemplate"]').value,
      xSpacing: parseInt(form.querySelector('[name="xSpacing"]').value) || 200,
      ySpacing: parseInt(form.querySelector('[name="ySpacing"]').value) || 100,
      angle: parseInt(form.querySelector('[name="angle"]').value) || -30,
      opacity: parseFloat(form.querySelector('[name="opacity"]').value) || 0.1,
      fontSize: parseInt(form.querySelector('[name="fontSize"]').value) || 14,
      antiCopy: form.querySelector('[name="antiCopy"]').checked,
      applyToMobile: form.querySelector('[name="applyToMobile"]').checked
    };
  }

  // Process placeholder variables
  function processPlaceholders(template) {
    var text = template;
    text = text.replace(/\$\{user\}/g, 'DemoUser');
    text = text.replace(/\$\{timestamp\}/g, new Date().toLocaleString());
    return text;
  }

  // Auto-update preview when form changes
  function setupAutoPreview() {
    var form = document.querySelector('.watermark-config-form');
    var inputs = form.querySelectorAll('input, textarea');
    
    inputs.forEach(function(input) {
      input.addEventListener('change', function() {
        setTimeout(previewWatermark, 100);
      });
      
      if (input.type === 'range' || input.type === 'number') {
        input.addEventListener('input', function() {
          setTimeout(previewWatermark, 100);
        });
      }
    });
  }

  // Initialize when DOM is ready
  document.addEventListener('DOMContentLoaded', function() {
    setupAutoPreview();
    previewWatermark();
  });

})();
</script>
{{/html}}

## Export current configuration as JavaScript for the watermark engine
#set($safeTextTemplate = $textTemplate.replaceAll("'", "\'"))

{{html}}
<script type="text/javascript">
  // Export configuration for watermark engine
  window.watermarkConfig = {
    enabled: #if($enabled == 1)true#{else}false#end,
    textTemplate: '$safeTextTemplate',
    xSpacing: $xSpacing,
    ySpacing: $ySpacing,
    angle: $angle,
    opacity: $opacity,
    fontSize: $fontSize,
    antiCopy: #if($antiCopy == 1)true#{else}false#end,
    applyToMobile: #if($applyToMobile == 1)true#{else}false#end
  };

  // Update watermark engine if available
  if (typeof window.XWikiWatermarkEngine !== 'undefined') {
    window.XWikiWatermarkEngine.updateConfig(window.watermarkConfig);
  }
</script>
{{/html}}
#else
## User is not admin, show access denied message

= Access Denied =

{{error}}
**Access Denied**

You need administrator privileges to access the watermark configuration.

Only members of the **XWikiAdminGroup** can configure watermark settings.
{{/error}}

[[Return to Home>>WatermarkExtension.WebHome]]

#end
## End of admin privilege check
{{/velocity}}
]]>
</content>
</xwikidoc>
